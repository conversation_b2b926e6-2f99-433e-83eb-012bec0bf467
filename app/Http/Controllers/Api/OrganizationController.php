<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\ActivitySearchRequest;
use App\Http\Requests\Api\GeoSearchRequest;
use App\Http\Requests\Api\OrganizationSearchRequest;
use App\Http\Resources\OrganizationResource;
use App\Models\Activity;
use App\Models\Organization;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

/**
 * @OA\Info(
 *     version="1.0.0",
 *     title="Organizations API",
 *     description="API для работы с организациями, зданиями и видами деятельности"
 * )
 * @OA\Server(
 *     url="/api",
 *     description="API Server"
 * )
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     description="Введите ваш статический API ключ"
 * )
 * @OA\Tag(
 *     name="Organizations",
 *     description="API для работы с организациями"
 * )
 *
 * @OA\Schema(
 *     schema="Organization",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="ООО Компания"),
 *     @OA\Property(property="description", type="string", nullable=true, example="Описание организации"),
 *     @OA\Property(property="website", type="string", nullable=true, example="https://example.com"),
 *     @OA\Property(property="email", type="string", nullable=true, example="<EMAIL>"),
 *     @OA\Property(property="building_id", type="integer", example=1),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="building", ref="#/components/schemas/Building"),
 *     @OA\Property(property="phones", type="array", @OA\Items(ref="#/components/schemas/Phone")),
 *     @OA\Property(property="activities", type="array", @OA\Items(ref="#/components/schemas/Activity"))
 * )
 *
 * @OA\Schema(
 *     schema="Building",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="address", type="string", example="ул. Пример, 123"),
 *     @OA\Property(property="latitude", type="number", format="float", example=55.751244),
 *     @OA\Property(property="longitude", type="number", format="float", example=37.618423),
 *     @OA\Property(property="postal_code", type="string", nullable=true, example="123456"),
 *     @OA\Property(property="city", type="string", example="Москва"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 *
 * @OA\Schema(
 *     schema="Phone",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="number", type="string", example="+7 (495) 123-45-67"),
 *     @OA\Property(property="type", type="string", example="main", enum={"main", "fax", "mobile"}),
 *     @OA\Property(property="organization_id", type="integer", example=1),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 *
 * @OA\Schema(
 *     schema="Activity",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="Торговля"),
 *     @OA\Property(property="code", type="string", nullable=true, example="47.11"),
 *     @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
 *     @OA\Property(property="description", type="string", nullable=true, example="Розничная торговля"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 *
 * @OA\Schema(
 *     schema="OrganizationCollection",
 *     type="object",
 *     @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/Organization"))
 * )
 *
 * @OA\Schema(
 *     schema="ValidationError",
 *     type="object",
 *     @OA\Property(property="message", type="string", example="The given data was invalid."),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         @OA\AdditionalProperties(
 *             type="array",
 *             @OA\Items(type="string")
 *         )
 *     )
 * )
 *
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(property="error", type="string", example="Unauthorized"),
 *     @OA\Property(property="message", type="string", example="Invalid API key")
 * )
 */
class OrganizationController
{
    /**
     * @OA\Get(
     *     path="/api/organizations/{id}",
     *     summary="Получить организацию по ID",
     *     description="Возвращает детальную информацию об организации с связанными данными",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         description="ID организации",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Успешный ответ",
     *         @OA\JsonContent(ref="#/components/schemas/Organization")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Организация не найдена",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="No query results for model [App\\Models\\Organization] 1")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function show(int $id): OrganizationResource
    {
        $organization = Organization::query()
            ->with(['building', 'phones', 'activities'])
            ->findOrFail($id);

        return new OrganizationResource($organization);
    }

    /**
     * @OA\Get(
     *     path="/api/organizations/building/{buildingId}",
     *     summary="Получить организации по ID здания",
     *     description="Возвращает список всех организаций, расположенных в указанном здании",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="buildingId",
     *         description="ID здания",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список организаций",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getByBuilding(int $buildingId): AnonymousResourceCollection
    {
        $organizations = Organization::query()
            ->where(['building_id' => $buildingId])
            ->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }

    /**
     * @OA\Get(
     *     path="/api/organizations/activity/{activityId}",
     *     summary="Получить организации по виду деятельности",
     *     description="Возвращает список организаций, занимающихся указанным видом деятельности",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="activityId",
     *         description="ID вида деятельности",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список организаций",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getByActivity(int $activityId): AnonymousResourceCollection
    {
        $organizations = Organization::query()
            ->whereHas('activities', function ($query) use ($activityId) {
                $query->where(['activity_id' => $activityId]);
            })
            ->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }

    /**
     * @OA\Post(
     *     path="/api/organizations/search/activity-tree",
     *     summary="Поиск по дереву видов деятельности",
     *     description="Ищет организации по указанному виду деятельности и всем его дочерним видам деятельности",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"activity_id"},
     *             @OA\Property(property="activity_id", type="integer", description="ID родительского вида деятельности", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список найденных организаций",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function searchByActivityTree(ActivitySearchRequest $request): AnonymousResourceCollection
    {
        $activityId = $request->validated()['activity_id'];

        $childrenIds = Activity::query()
            ->where('parent_id', $activityId)
            ->orWhere('parent_id', function ($query) use ($activityId) {
                $query->select('id')
                    ->from('activities')
                    ->where('parent_id', $activityId);
            })
            ->pluck('id')
            ->push($activityId)
            ->toArray();

        $organizations = Organization::query()
            ->whereHas('activities', function ($query) use ($childrenIds) {
                $query->whereIn('activity_id', $childrenIds);
            })->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }

    /**
     * @OA\Post(
     *     path="/api/organizations/search/name",
     *     summary="Поиск организаций по названию",
     *     description="Ищет организации по частичному совпадению названия (регистронезависимый поиск)",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name"},
     *             @OA\Property(property="name", type="string", description="Название или часть названия организации", example="ООО")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список найденных организаций",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function searchByName(OrganizationSearchRequest $request): AnonymousResourceCollection
    {
        $name = $request->validated()['name'];

        $organizations = Organization::query()
            ->where('name', 'LIKE', "%{$name}%")
            ->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }

    /**
     * @OA\Post(
     *     path="/api/organizations/search/geo/radius",
     *     summary="Поиск организаций по радиусу",
     *     description="Ищет организации в указанном радиусе от заданной точки (в километрах)",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"latitude", "longitude", "radius"},
     *             @OA\Property(property="latitude", type="number", format="float", description="Широта центральной точки", example=55.751244, minimum=-90, maximum=90),
     *             @OA\Property(property="longitude", type="number", format="float", description="Долгота центральной точки", example=37.618423, minimum=-180, maximum=180),
     *             @OA\Property(property="radius", type="number", format="float", description="Радиус поиска в километрах", example=5.0, minimum=0.1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список организаций в указанном радиусе",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getByGeoRadius(GeoSearchRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();
        $lat = $validated['latitude'];
        $lng = $validated['longitude'];
        $radius = $validated['radius'];

        $organizations = Organization::query()
            ->whereHas('building', function ($query) use ($lat, $lng, $radius) {
                $query->selectRaw("
                *,
                (6371 * acos(
                    cos(radians(?)) * cos(radians(latitude)) *
                    cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) * sin(radians(latitude))
                )) AS distance
            ", [$lat, $lng, $lat])
                    ->havingRaw('distance <= ?', [$radius]);
            })
            ->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }

    /**
     * @OA\Post(
     *     path="/api/organizations/search/geo/rectangle",
     *     summary="Поиск организаций в прямоугольной области",
     *     description="Ищет организации внутри прямоугольной области, определенной координатами",
     *     tags={"Organizations"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"min_lat", "max_lat", "min_lng", "max_lng"},
     *             @OA\Property(property="min_lat", type="number", format="float", description="Минимальная широта области", example=55.7, minimum=-90, maximum=90),
     *             @OA\Property(property="max_lat", type="number", format="float", description="Максимальная широта области", example=55.8, minimum=-90, maximum=90),
     *             @OA\Property(property="min_lng", type="number", format="float", description="Минимальная долгота области", example=37.5, minimum=-180, maximum=180),
     *             @OA\Property(property="max_lng", type="number", format="float", description="Максимальная долгота области", example=37.7, minimum=-180, maximum=180)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список организаций в указанной области",
     *         @OA\JsonContent(ref="#/components/schemas/OrganizationCollection")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getByGeoRectangle(GeoSearchRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $organizations = Organization::query()
            ->whereHas('building', function ($query) use ($validated) {
                $query->whereBetween('latitude', [$validated['min_lat'], $validated['max_lat']])
                    ->whereBetween('longitude', [$validated['min_lng'], $validated['max_lng']]);
            })
            ->with(['building', 'phones', 'activities'])
            ->get();

        return OrganizationResource::collection($organizations);
    }
}
