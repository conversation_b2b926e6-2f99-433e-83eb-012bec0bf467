{"openapi": "3.0.0", "info": {"title": "Organizations API", "description": "API для работы с организациями, зданиями и видами деятельности", "version": "1.0.0"}, "servers": [{"url": "/api", "description": "API Server"}], "paths": {"/api/organizations/{id}": {"get": {"tags": ["Organizations"], "summary": "Получить организацию по ID", "description": "Возвращает детальную информацию об организации с связанными данными", "operationId": "11826ee077ae441c61f2bee8476e2a14", "parameters": [{"name": "id", "in": "path", "description": "ID организации", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "404": {"description": "Организация не найдена", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "No query results for model [App\\\\Models\\\\Organization] 1"}}, "type": "object"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/building/{buildingId}": {"get": {"tags": ["Organizations"], "summary": "Получить организации по ID здания", "description": "Возвращает список всех организаций, расположенных в указанном здании", "operationId": "4a27e095a6c8752e92df909c27d7df6e", "parameters": [{"name": "buildingId", "in": "path", "description": "ID здания", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Список организаций", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/activity/{activityId}": {"get": {"tags": ["Organizations"], "summary": "Получить организации по виду деятельности", "description": "Возвращает список организаций, занимающихся указанным видом деятельности", "operationId": "b03a17d26be10f29616d8ada20c9a360", "parameters": [{"name": "activityId", "in": "path", "description": "ID вида деятельности", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Список организаций", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/activity-tree": {"post": {"tags": ["Organizations"], "summary": "Поиск по дереву видов деятельности", "description": "Ищет организации по указанному виду деятельности и всем его дочерним видам деятельности", "operationId": "edb85d7b00cc6fd8c3d40f04e8185e61", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["activity_id"], "properties": {"activity_id": {"description": "ID родительского вида деятельности", "type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "Список найденных организаций", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/name": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций по названию", "description": "Ищет организации по частичному совпадению названия (регистронезависимый поиск)", "operationId": "549a567d0e8da0777023f7f3d8cdb664", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"description": "Название или часть названия организации", "type": "string", "example": "ООО"}}, "type": "object"}}}}, "responses": {"200": {"description": "Список найденных организаций", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/radius": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций по радиусу", "description": "Ищет организации в указанном радиусе от заданной точки (в километрах)", "operationId": "f8db8db2c672d1ebffe2e34157b874f3", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["latitude", "longitude", "radius"], "properties": {"latitude": {"description": "Широта центральной точки", "type": "number", "format": "float", "maximum": 90, "minimum": -90, "example": 55.751244}, "longitude": {"description": "Долгота центральной точки", "type": "number", "format": "float", "maximum": 180, "minimum": -180, "example": 37.618423}, "radius": {"description": "Радиус поиска в километрах", "type": "number", "format": "float", "minimum": 0.1, "example": 5}}, "type": "object"}}}}, "responses": {"200": {"description": "Список организаций в указанном радиусе", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/rectangle": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций в прямоугольной области", "description": "Ищет организации внутри прямоугольной области, определенной координатами", "operationId": "e01c779292b7b364aaa4935dc7d945e8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["min_lat", "max_lat", "min_lng", "max_lng"], "properties": {"min_lat": {"description": "Минимальная широта области", "type": "number", "format": "float", "maximum": 90, "minimum": -90, "example": 55.7}, "max_lat": {"description": "Максимальная широта области", "type": "number", "format": "float", "maximum": 90, "minimum": -90, "example": 55.8}, "min_lng": {"description": "Минимальная долгота области", "type": "number", "format": "float", "maximum": 180, "minimum": -180, "example": 37.5}, "max_lng": {"description": "Максимальная долгота области", "type": "number", "format": "float", "maximum": 180, "minimum": -180, "example": 37.7}}, "type": "object"}}}}, "responses": {"200": {"description": "Список организаций в указанной области", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "401": {"description": "Не авторизован", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"Organization": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "ООО Компания"}, "description": {"type": "string", "example": "Описание организации", "nullable": true}, "website": {"type": "string", "example": "https://example.com", "nullable": true}, "email": {"type": "string", "example": "<EMAIL>", "nullable": true}, "building_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000000Z"}, "building": {"$ref": "#/components/schemas/Building"}, "phones": {"type": "array", "items": {"$ref": "#/components/schemas/Phone"}}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}, "type": "object"}, "Building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "ул. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 123"}, "latitude": {"type": "number", "format": "float", "example": 55.751244}, "longitude": {"type": "number", "format": "float", "example": 37.618423}, "postal_code": {"type": "string", "example": "123456", "nullable": true}, "city": {"type": "string", "example": "Москва"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "Phone": {"properties": {"id": {"type": "integer", "example": 1}, "number": {"type": "string", "example": "+7 (495) 123-45-67"}, "type": {"type": "string", "enum": ["main", "fax", "mobile"], "example": "main"}, "organization_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "Activity": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Торговля"}, "code": {"type": "string", "example": "47.11", "nullable": true}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "description": {"type": "string", "example": "Розничная торговля", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "OrganizationCollection": {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}, "type": "object"}, "ValidationError": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "type": "object"}, "ErrorResponse": {"properties": {"error": {"type": "string", "example": "Unauthorized"}, "message": {"type": "string", "example": "Invalid API key"}}, "type": "object"}}, "securitySchemes": {"bearerAuth": {"type": "http", "description": "Введите ваш статический API ключ", "scheme": "bearer"}}}, "tags": [{"name": "Organizations", "description": "API для работы с организациями"}]}